/**
 * StoreList 组件使用示例
 * 
 * 展示如何使用 storeMsg 功能和 useImperativeHandle 暴露的方法
 */

import { useRef, useCallback } from 'react'
import type { StoreListItem } from '@ninebot/core'

import StoreList from './StoreList'

// 定义 ref 类型
interface StoreListRef {
  storeMsg: string
  setStoreMsg: (msg: string) => void
}

const StoreListExample = () => {
  // 创建 ref 来访问 StoreList 的方法
  const storeListRef = useRef<StoreListRef>(null)

  // 示例：获取当前的门店消息
  const handleGetStoreMsg = useCallback(() => {
    if (storeListRef.current) {
      const currentMsg = storeListRef.current.storeMsg
      console.log('当前门店消息:', currentMsg)
      alert(`当前门店消息: ${currentMsg || '无消息'}`)
    }
  }, [])

  // 示例：手动设置门店消息
  const handleSetStoreMsg = useCallback(() => {
    if (storeListRef.current) {
      storeListRef.current.setStoreMsg('这是一条自定义的门店消息')
      console.log('已设置自定义门店消息')
    }
  }, [])

  // 示例：清空门店消息
  const handleClearStoreMsg = useCallback(() => {
    if (storeListRef.current) {
      storeListRef.current.setStoreMsg('')
      console.log('已清空门店消息')
    }
  }, [])

  return (
    <div className="p-4">
      <h2 className="mb-4 text-xl font-bold">StoreList 组件示例</h2>
      
      {/* 控制按钮 */}
      <div className="mb-4 space-x-2">
        <button
          onClick={handleGetStoreMsg}
          className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
        >
          获取门店消息
        </button>
        <button
          onClick={handleSetStoreMsg}
          className="rounded bg-green-500 px-4 py-2 text-white hover:bg-green-600"
        >
          设置自定义消息
        </button>
        <button
          onClick={handleClearStoreMsg}
          className="rounded bg-red-500 px-4 py-2 text-white hover:bg-red-600"
        >
          清空消息
        </button>
      </div>

      {/* StoreList 组件 */}
      <div className="h-96 border rounded">
        <StoreList
          ref={storeListRef}
          productId="example-product-id"
          store={[]}
          setStore={() => {}}
          setBtnLoading={() => {}}
          curStore={null}
          setCurStore={() => {}}
          address={{
            region: '北京市',
            city: '北京市',
            district: '朝阳区'
          }}
          userLocation={{
            longitude: 116.397428,
            latitude: 39.90923
          }}
        />
      </div>

      <div className="mt-4 text-sm text-gray-600">
        <h3 className="font-semibold">功能说明：</h3>
        <ul className="mt-2 list-disc pl-5">
          <li>storeMsg 状态变量用于存储门店相关的消息提示</li>
          <li>从 API 响应的 product_storesV2.message 字段自动设置消息</li>
          <li>在门店列表为空时优先显示 storeMsg，如果为空则显示默认国际化文本</li>
          <li>通过 useImperativeHandle 暴露 storeMsg 和 setStoreMsg 给父组件</li>
          <li>支持父组件通过 ref 访问和修改门店消息状态</li>
        </ul>
      </div>
    </div>
  )
}

export default StoreListExample
